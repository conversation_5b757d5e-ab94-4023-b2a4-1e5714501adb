import { requestWithProjectId, GET } from '@/request';

// 获取SSH密钥信息
export const getSshKeyInfo = (data) => requestWithProjectId.GET('/web/project/v1/ssh-key/get', data);

// 创建/重新创建SSH密钥 - 返回JSON响应
export const createSshKey = (data) => requestWithProjectId.GET('/web/project/v1/ssh-key/create', data);

// 下载SSH私钥 - 返回文件下载
export const downloadSshKey = (data) => requestWithProjectId.requestBlobMethod('/web/project/v1/ssh-key/download', 'GET', data, { responseType: 'blob' });

// 发送短信验证码
export const sendSmsCode = (data) => requestWithProjectId.GET('/web/project/v1/member/smscode', data);

// 获取侧边栏状态
export const getSidebarStatus = (data) => GET('/web/display/v1/sidebar-status', data);
